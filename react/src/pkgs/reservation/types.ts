import { string, z } from 'zod'

export const reservable = z.object({
    ID: string(),
    Workspace: string(),
    CurrentEditor: string().nullish(),
    EditingSession: string().nullish(),
    ExtendedLock: string().nullish()
})

export type Reservable = z.infer<typeof reservable>
export type ReservableTable = Reservable & { Table: string }
export type ReservableUpdate = ReservableTable & { NextEditingSession: string | null }
export type NullableDate = string | null | undefined | Date

%syntax-version=1.0.0
%project=cm_tenant_db_DeploySchema
%uri=https://cm_tenant_db.local/Deploy_Schema

BaseEnums 2020-06-12T20:24:46Z   <richard@nixCM> # Add base enums
AccountTable 2020-06-12T20:24:46Z   <richard@nixCM> # Implement Account Table
ContentTable 2020-06-15T12:29:33Z   <richard@nixCM> # Implement Content Table
MediaTable 2020-06-15T12:29:33Z   <richard@nixCM> # Implement Media Table
TagTable 2020-06-15T12:29:33Z   <richard@nixCM> # Implement Tag Table
RoleTable 2020-08-18T13:54:48Z   <richard@nixCM> # Implement Role Table
SecurityGroupTable 2020-08-18T13:54:56Z   <richard@nixCM> # Implement Security Group Table
AccountSecurityGroupTable 2020-08-18T14:38:01Z   <richard@nixCM> # Implement Account Security Group Link Table
media_content_id 2020-08-25T23:12:33Z   <nick@nixCM> # implement: content_id
ContentTable_pagelayout_pagestyle 2020-08-27T00:19:12Z   <richard@nixCM> # reuse pagelayout as page_type
SecurityGroupTable_active 2020-09-22T22:13:56Z   <nick@nixCM> # implement active bool
MediaTable_active 2020-09-22T22:22:49Z   <nick@nixCM> # implement active bool
BaseEnums_tagtype 2020-10-07T15:00:34Z   <nick@nixCM> # implement base enums for tag
TagTable_tagtype 2020-10-07T15:14:25Z   <nick@nixCM> # implement tag type baseEnum into tag
ContentTable_navigation_id 2020-10-07T21:23:26Z   <nick@nixCM> # implement navigation_id for IM-344
BaseEnums_contenttype_navigation 2020-10-08T14:41:11Z   <nick@nixCM> # implement navigation content_type
ContentTable_StartDate_EndDate 2020-10-20T15:20:42Z   <richard@nixCM> # Implement StateDate and EndDate for Event Content Type
AccountTable_role_to_privacylevel 2021-01-11T20:29:30Z   <nick@nixCM> # re-name role to privacylevel
RoleTable_drop_seeallsites 2021-01-14T21:40:46Z   <nick@nixCM> # drop See-all-sites override on RoleTable
RoleTable_alter_access 2021-01-14T21:54:42Z   <nick@nixCM> # alter access_level to new permissions
BusRouteTable 2021-02-03T21:02:50Z   <nick@nixCM> # implement bus route table
BusAreaTable 2021-02-03T21:08:07Z   <nick@nixCM> # implement bus area table
BusStatusTable 2021-02-03T21:08:18Z   <nick@nixCM> # implement bus status table
BaseEnums_statustype 2021-02-03T22:32:35Z   <nick@nixCM> # implement status_type for bus_route
BusStatusTable_statustype 2021-02-04T00:11:42Z   <nick@nixCM> # implement status type into bus_status
ContentTable_priority 2021-02-17T16:00:27Z   <nick@nixCM> # implement priority float on content
ContentTable_settings 2021-03-04T18:31:21Z   <nick@nixCM> # implement settings into ContentTable
NavigationTable 2021-03-18T19:51:06Z   <nick@nixCM> # implement navigation table
BaseEnums_navigationtype 2021-03-19T15:26:50Z   <nick@nixCM> # implement navigation types
NavigationTable_type 2021-03-22T22:05:08Z   <nick@nixCM> # implement type on navigationTable
BaseEnums_contenttype_external_link 2021-11-08T22:10:01Z   <nick@nixCM> # add external_link contenttype to baseenums
NavigationTable_treeindex 2021-11-12T19:43:19Z   <nick@nixCM> # implement navigation tree-index
DocumentTable 2022-02-11T17:27:51Z   <nick@nixCM> # implement document table
BaseEnums_documenttype 2022-02-24T19:10:45Z   <nick@nixCM> # implement documentType to documents table
DocumentTable_type_privacy 2022-02-24T19:16:05Z   <nick@nixCM> # implement document_type and privacy_level - remove route
MediaTable_alt 2022-03-15T17:47:47Z   <nick@nixCM> # implement the alt attribute on media
ContentTable_media_id 2022-03-17T21:40:02Z   <nick@nixCM> # implement media_id on content
ContentTable_published 2022-03-28T21:50:52Z   <nick@nixCM> # implement published for drafting
BaseEnums_contenttype_alert 2022-04-15T03:19:38Z   <nick@nixCM> # implement alert contentType
RoleTable_mod_alerts 2022-04-26T19:22:01Z   <nick@nixCM> # implement mod_alerts role
ContentTable_removeTags 2022-06-02T15:35:55Z   <nick@nixCM> # remove the tags field from content
ContentTagsLinkTable 2022-06-02T15:37:28Z   <nick@nixCM> # implement the content tags link table
BaseEnums_contenttype_distributed_page 2022-07-18T20:25:14Z developer <developer@imagineeverything-developer> # implement distributed_page content_type
ThirdPartyContentTable 2022-08-16T19:16:00Z developer <developer@imagineeverything-developer> # implement the third_party_content table
RedirectRuleTable 2022-08-24T22:10:54Z developer <developer@imagineeverything-developer> # implement redirect_rule table
LegacyUrl 2022-08-25T22:10:24Z developer <developer@imagineeverything-developer> # add legacy_url table
ContentTable_addLowerIndexOnRoute 2022-09-13T13:13:34Z developer <developer@imagineeverything-developer> # add lower index for route to content table
FullTextSearchIndex 2022-09-14T15:18:14Z developer <developer@imagineeverything-developer> # add full text column and index for content and document tables
AccountTable_settings_hstore_to_jsonb 2022-10-31T21:08:41Z developer <developer@imagineeverything-developer> # alter account tables settings column to use jsonb instead of hstore as a pre-requisite for completing gorm v2 upgrade
TagTable_default_id 2022-11-16T19:03:04Z developer <developer@imagineeverything-developer> # implement default on id_pkey
MediaTable_default_id 2022-11-16T19:03:17Z developer <developer@imagineeverything-developer> # implement default id on pkey
DocumentTable_default_id 2022-11-16T19:03:31Z developer <developer@imagineeverything-developer> # implement default id on pkey
ContentTags_AddCompositeKey 2022-12-01T20:39:59Z developer <developer@imagineeverything-developer> # implement composite key instead of current pkey with two foreign keys
2023-02-09-NotificationsTables_init 2023-02-09T12:58:54Z developer <<EMAIL>> # create tables for notifications
2023-03-01-AccountTable_IsAdmin 2023-03-01T12:58:54Z developer <<EMAIL>> # add is_admin to account table
BaseEnums_navigationtype_department 2023-01-20T23:44:59Z developer <developer@imagineeverything-developer> # add navigationType department
NavigationTable_department_id 2023-01-20T23:47:03Z developer <developer@imagineeverything-developer> # implement department_id on navigationTable
ContentTable_addDepartmentId 2023-02-01T21:28:57Z developer <developer@imagineeverything-developer> # implement department_id on content table
2023-05-18-docs_images_addDepartmentId 2023-05-18T21:28:57Z developer <developer@imagineeverything-developer> # implement department_id on media and document tables
2023-06-21-remove-navigation_id 2023-06-21T21:28:57Z developer <developer@imagineeverything-developer> # remove navigation_id from content table
2023-07-13-FullTextSearchIndexFixCleanup 2023-07-13T11:32:30Z anatoly <anatoly@imagineeverything-developer> # remove unnecessary cleanup from full text search index
2023-07-18-LogAudit 2023-07-18T11:32:30Z anatoly <anatoly@imagineeverything-developer> # refactor AuditRecord table
2023-07-24-FullTextSearchIndexRemoveDashesUnderscoresDots 2023-07-24T11:32:30Z anatoly <anatoly@imagineeverything-developer> # remove "-" "_" "." from full text search index
2023-08-03-InlineChildren 2023-08-03T11:32:30Z anatoly <anatoly@imagineeverything-developer> # add inline children to navigation table
2023-08-15-ExtendTopicTable 2023-08-15T11:32:30Z anatoly <anatoly@imagineeverything-developer> # extend topic table
2023-09-26-MediaTable_origin 2023-09-26T20:47:16Z developer <developer@imagineeverything-developer> # add the origin column to Media
2023-09-29-SettingsTable 2023-09-29T20:47:16Z anatoly <anatoly@imagineeverything-developer> # add settings table
2023-10-03-PATokensTable 2023-10-03T20:47:16Z anatoly <anatoly@imagineeverything-developer> # add PATokens table
2023-10-04-Settings_add_public 2023-10-03T20:47:16Z anatoly <anatoly@imagineeverything-developer> # add public column to settings table
2023-10-10-Role_add_instagram 2023-10-10T18:12:06Z developer <developer@imagineeverything-developer> # add temporary instagram column to role model
2023-10-17-Account_SecurityGroup_user_management 2023-10-17T20:47:16Z anatoly <anatoly@imagineeverything-developer> # add columns to account and security_group tables related to user management
2023-11-07-migrate-accounts 2023-11-07T20:47:16Z anatoly <anatoly@imagineeverything-developer> # migrate accounts to new schema
2023-11-08-Settings_change_uniqueness 2023-11-08T20:47:16Z anatoly <anatoly@imagineeverything-developer> # update uniqueness of settings table [by (lower(name), site_id)]
2023-11-23-Granular_Permissions 2023-11-23T20:47:16Z anatoly <anatoly@imagineeverything-developer> # update scopes column on role table
2023-11-21-StructureTable_content_structure_id 2023-11-21T23:28:53Z developer <developer@imagineeverything-developer> # add structure table and alter content table to include structure_id and new fragment content_type
2023-11-30_SecurityGoup_fix_index 2023-11-30T23:28:53Z developer <anatoly@imagineeverything-developer> # fix full text search index to support nullable site_id
2023-12-19_ContentTable_add_meta 2023-12-18T18:29:52Z developer <developer@imagineeverything-developer> # adding meta column to Content table
2023-12-11-Content_PublishPeriod 2023-12-11T23:28:53Z developer <anatoly@imagineeverything-developer> # add publish period to content table (published_at, expires_at)
2023-12-11-Lists 2023-12-11T23:28:53Z developer <anatoly@imagineeverything-developer> # Lists table
2024-01-26_ContentTable_migrate_publish_period 2024-01-26T23:28:53Z developer <anatoly@imagineeverything-developer> # Migrate publish period to new columns
2024-02-02-Role-Type 2024-02-02T22:23:53Z developer <developer@ie-developer-nix23-05> # 2024-02-02 Add type column to role table
2024-02-09-Lists_Tags 2023-02-09T23:28:53Z developer <anatoly@imagineeverything-developer> # Lists table: add tags
2024-03-20-Templates_Structures 2024-03-20T20:55:48Z developer <anatoly@imagineeverything-developer> # Content table: add compatible structure IDs for templates
2024-04-03-Lists_drop_ux 2024-04-03T13:28:24Z developer <anatoly@imagineeverything-developer> # Lists table: drop unique index on name
2024-04-22-Forms 2024-04-22T20:27:20Z anatoly <<EMAIL>> # Create a table to store submitted forms
2024-05-17-temporal-tables-sys 2024-05-17T20:05:17Z root <root@1911dbe61ec0> # Temporal tables tooling
2024-05-18-history-tables 2024-05-17T20:12:54Z root <root@1911dbe61ec0> # History tables
2024-07-11-Add-Tags-Tracking-Columns 2024-07-12T08:50:37Z System Administrator <<EMAIL>> # Add tracking columns to tag table
2024-07-12-Alter-Settings-Table-SiteId_To_SiteIds 2024-07-12T17:55:13Z developer <<EMAIL>> # alter Settings.SiteID to Settings.Sites
2024-10-04-Distributed-Lists 2024-10-04T11:55:13Z developer <<EMAIL>> # alter Lists table to support distributed lists
2024-11-05-SearchIndex 2024-11-05T10:25:11Z developer <<EMAIL>> # add search index table
2024-11-25-Promotions 2024-11-25T17:12:27Z anatoly <<EMAIL>> # add promotions table
2024-12-04-Suggestions 2024-12-04T01:39:16Z root <root@ddbd09656471> # add search_suggestions table
2024-12-07-Indexes 2024-12-08T06:48:23Z root <root@ddbd09656471> # optimize path queries, etc.
2025-02-10-Resources 2025-02-10T22:58:20Z root <anatoly@ddbd09656471> # add resources table
2025-02-13-content_add_tag_ids 2025-02-14T06:54:34Z root <root@8fc63ea7e6ae> # add tag_ids to content table and migrate from content_Tags
2025-03-01-Image-Crops 2025-02-11T10:32:55Z jackson <<EMAIL>> # 3846 Storage Manager | Image Crops
2025-03-10-Add_Map_Overlay_Polygon_Table 2024-12-09T20:15:08Z root <root@5c5e20c19c39> # add table for schoolfinder polygon search
2025-04-04-Separate-content-types 2025-04-05T21:41:07Z jackson <<EMAIL>> # -
2025-04-05-Document-title 2025-03-31T20:02:43Z jackson <<EMAIL>> # feature [4345] Add document title
2025-04-09-IndexesForQueries 2025-03-04T19:35:34Z root <root@ddbd09656471> # add indexes for queries optimization
2025-04-09-Queries 2025-03-10T19:33:54Z root <root@ddbd09656471> # add queries related tables
2025-04-22-Query-by-folders 2025-04-22T18:48:49Z root <root@ddbd09656471> # add generated column with correct path for querying
2025-05-05-Settings_for_search 2025-05-05T19:06:49Z root <root@ddbd09656471> # settings for search
2025-05-20-Content-add-reservable 2025-05-20T21:19:53Z root <root@8fc63ea7e6ae> # add reservable columns to content table
2025-05-23-Content-alter-route-varchar 2025-05-16T21:07:56Z root <root@8fc63ea7e6ae> # update route type from varchar(256) to varchar(512) to accommodate google calendar links
2025-05-28-Workspaces 2025-04-29T23:09:27Z root <root@ddbd09656471> # workspaces

import { NullableDate } from '@/pkgs/reservation/types'
import moment from 'moment'

export const Second = 1000
export const Minute = 60000

export function extend(duration: number, time?: NullableDate) {
    const f = time ? time : new Date()
    return moment(f).tz('utc').add(millisecondsToMinutes(duration), 'm').toISOString()
}

export function millisecondsToMinutes(duration: number) {
    return duration / 60000
}

export function utc(date: NullableDate) {
    return moment(date).tz('UTC')
}

export function isFutureTimestamp(time: NullableDate) {
    if (time == null) {
        return false
    }
    return utc(new Date()).isBefore(utc(time))
}

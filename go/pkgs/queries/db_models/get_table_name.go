package db_models

import "fmt"

func GetTableName(contentTypes []string) (string, error) {
	if len(contentTypes) == 0 {
		return "content", nil
	}

	if len(contentTypes) == 1 && contentTypes[0] == "document" {
		return "document", nil
	}
	if len(contentTypes) == 1 && contentTypes[0] == "image" {
		return "media", nil
	}

	for _, contentType := range contentTypes {
		switch contentType {
		case "page":
		case "news":
		case "event":
		case "fragment":
			continue

		case "document":
			return "", fmt.Errorf("can't use `document` with other types: %v", contentTypes)
		default:
			return "", fmt.Errorf("unsupported content type: %s", contentType)
		}
	}
	return "content", nil
}

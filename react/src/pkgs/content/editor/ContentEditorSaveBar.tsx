import { Content, ContentType, PublishPeriod } from '../types'
import React, { useEffect, useMemo, useRef } from 'react'
import { handlePreviewAtom } from '@/common/components/selectors/SiteSelectorForContent'
import { atom, useAtom, useSetAtom } from 'jotai'
import { Card, Divider, FormControl, Stack, Typography } from '@mui/material'
import VisibilityIcon from '@mui/icons-material/Visibility'
import Button from '@mui/material/Button'
import { ColourHexValue, colourWithOpacity, colours } from '@/common/colours'
import { useInputDisabler } from './imported-content/useInputDisabler'
import { useAppContext } from '../../auth/atoms'
import { bannerHeightAtom } from '@/app/AppTopBar/InstagramErrorsBanner'
import { appTopBarHeightAtom, drawerWidth } from '@/app/AppTopBar/AppTopBar'
import BackButton from '@/common/components/BackButton'
import { drawerIsOpenAtom } from '@/app/ApplicationWrapper'
import useContentDiff from './diff/useContentDiff'
import { primaryTheme } from '@/app/theme'
import { SplitButtonV2 } from '@/pkgs/queries/SplitButtonV2'
import { WorkspaceSelector } from '@/pkgs/workspaces/WorkspaceSelector'
import { useAppNavigation } from '@/app/useAppNavigation'
import { useParams } from 'react-router-dom'
import IconButton from '@mui/material/IconButton'
import { useContentQueries } from '@/pkgs/content/queries'
import HistoryIcon from '@mui/icons-material/History'
import moment from 'moment/moment'
import { CompareToLive } from '@/pkgs/content/editor/diff/CompareToLive'
import { ReservationSwitch } from '@/pkgs/reservation/ReservationSwitch'
import { Reservable } from '@/pkgs/reservation/types'

function getPathToContentType(content: Content | any) {
    const contentType = content?.type || content?.Type

    if (contentType == ContentType.Page) {
        return '/pages'
    } else if (contentType == ContentType.News) {
        return '/news'
    } else if (contentType == ContentType.Event) {
        return '/events'
    }

    return ''
}

export type publishStatus = 'draft' | 'published' | 'scheduled' | 'expired'
export const publishStatusColour: Record<publishStatus, string> = {
    draft: colours.warning,
    published: colours.published,
    scheduled: colours.base_blue,
    expired: colours.expired
}

export function getPublishStatus(
    publishAt: string | Date | null | undefined,
    expireAt: string | Date | null | undefined
): publishStatus {
    if (!publishAt) return 'draft'

    const now = new Date()
    if (publishAt && new Date(publishAt) > now) return 'scheduled'
    if (expireAt && new Date(expireAt) < now) return 'expired'
    if (publishAt) return 'published'
    return 'draft'
}

export const changeStatusLabel: { [key in publishStatus]: { [key in publishStatus]: string } } = {
    draft: {
        draft: 'Save as Draft',
        published: 'Publish',
        scheduled: 'Schedule',
        expired: 'Expire'
    },
    published: {
        draft: 'Unpublish and Save as Draft',
        published: 'Update Publication',
        scheduled: 'Schedule',
        expired: 'Expire'
    },
    scheduled: {
        draft: 'Unschedule and Save as Draft',
        published: 'Publish Now',
        scheduled: 'Schedule',
        expired: 'Expire'
    },
    expired: {
        draft: 'Save as Draft',
        published: 'Publish',
        scheduled: 'Schedule',
        expired: 'Expire'
    }
}

interface ContentEditorSaveBarProps<T extends Content> {
    serverValue: T
    value: T
    hasExternalChanges: boolean

    onAction(action: 'create' | 'save' | 'publish' | 'draft', state: T): void

    disabled?: boolean
    onChange?: (v: PublishPeriod) => void
    onRevert?: (newValue: T, callback?: () => void) => void
    onChangeMode?: (reservable: Reservable, checked: boolean) => void
}

export const containerEditorSaveBarHeightAtom = atom<number>(0)

export function ContentEditorSaveBar<T extends Content>({
    serverValue,
    value,
    hasExternalChanges,
    onAction,
    disabled,
    onChange,
    onChangeMode,
    onRevert
}: ContentEditorSaveBarProps<T>) {
    const { navigateTo } = useAppNavigation()
    const { id, workspace } = useParams()
    const [bannerHeight] = useAtom(bannerHeightAtom)
    const [appTopBarHeight] = useAtom(appTopBarHeightAtom)
    const [drawerIsOpen] = useAtom(drawerIsOpenAtom)
    const barRef = useRef<HTMLDivElement | null>(null)
    const setHeight = useSetAtom(containerEditorSaveBarHeightAtom)

    const [compareToOpen, setCompareToOpen] = React.useState(false)
    const [handlePreview] = useAtom(handlePreviewAtom)

    const publishStatus = useMemo(() => getPublishStatus(value.PublishAt, value.ExpireAt), [value])

    const current = getPublishStatus(value.PublishAt, value.ExpireAt)
    const original = getPublishStatus(serverValue.PublishAt, serverValue.ExpireAt)
    const createLabel = () => {
        return changeStatusLabel[original][current]
    }
    const evaluators = useAppContext()
    const hasPermission = React.useMemo(() => evaluators.action(value, 'update'), [value])
    const { isInputDisabled, isFullyDisabled } = useInputDisabler({ content: value, hasPermission })

    const { changed } = useContentDiff({
        serverValue: serverValue,
        value: value,
        hasExternalChanges: hasExternalChanges
    })

    useEffect(() => {
        if (!barRef?.current) {
            setHeight(0)
        } else {
            const height = barRef?.current.getBoundingClientRect().height
            setHeight(height)
        }
    }, [barRef?.current])

    // to make ts happy
    if (!id || !workspace) return null

    return (
        <>
            <div
                ref={barRef}
                style={{
                    position: 'fixed',
                    top: `${bannerHeight + appTopBarHeight}px`,
                    width: `calc(${'100%'} - ${drawerIsOpen ? drawerWidth : 0}px)`,
                    zIndex: 1001,
                    marginLeft: '-24px', // ApplicationWrapper padding
                    marginRight: '-24px',
                    transition: 'ease 0.5s',
                    borderBottom: changed ? `4px solid ${primaryTheme.palette.warning.main}` : undefined
                }}
            >
                <Card
                    style={{
                        paddingLeft: '3em',
                        paddingRight: '3em',
                        paddingTop: '0.25em',
                        paddingBottom: '0.25em',
                        overflow: 'visible',
                        justifyContent: 'space-between',
                        display: 'flex',
                        flexDirection: 'row',
                        gap: '16px',
                        alignItems: 'center'
                    }}
                >
                    <BackButton route={getPathToContentType(serverValue)} />

                    <ReservationSwitch entity={value} table={'content'} onChange={onChangeMode} disabled={disabled} />

                    <FormControl
                        variant='standard'
                        component='fieldset'
                        style={{ width: '100%' }}
                        sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'flex-end',
                            gap: '16px'
                        }}
                    >
                        <WorkspaceSelector
                            value={workspace}
                            onChange={(w) => navigateTo(`/content-editor/${value.ID}/${w}`)}
                        />

                        {value.Workspace !== 'live' && (
                            <CompareToLive
                                value={value}
                                open={compareToOpen}
                                onClose={() => setCompareToOpen(false)}
                                onRevert={onRevert}
                            />
                        )}

                        <div className='flex-row-align-center'>
                            <SplitButtonV2
                                variant={'contained'}
                                color={'success'}
                                button={
                                    <Button
                                        variant='contained'
                                        color='success'
                                        onClick={() => onAction('save', { ...value, Workspace: workspace })}
                                        disabled={Boolean(disabled) || isFullyDisabled}
                                    >
                                        Save {/*{createLabel()}*/}
                                    </Button>
                                }
                            >
                                <Stack
                                    sx={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '8px',
                                        padding: '8px',
                                        minWidth: '200px'
                                    }}
                                >
                                    {value.Workspace !== 'live' && (
                                        <>
                                            <Button
                                                color={'info'}
                                                variant='contained'
                                                onClick={() => setCompareToOpen(true)}
                                                disabled={Boolean(disabled) || isFullyDisabled}
                                            >
                                                Compare to Live
                                            </Button>

                                            <Button
                                                variant='contained'
                                                onClick={() => onAction('publish', value)}
                                                disabled={Boolean(disabled) || isFullyDisabled}
                                            >
                                                Publish to Live
                                            </Button>
                                        </>
                                    )}

                                    {value.Workspace === 'live' && (
                                        <Button
                                            color={'error'}
                                            variant='contained'
                                            onClick={() => onAction('draft', value)}
                                            disabled={Boolean(disabled) || isFullyDisabled}
                                        >
                                            Delete Live
                                        </Button>
                                    )}
                                </Stack>
                            </SplitButtonV2>
                        </div>
                        <Divider orientation='vertical' variant='middle' flexItem sx={{ marginX: '8px' }} />
                        <IconButton
                            title={'Preview'}
                            color={'success'}
                            onClick={() =>
                                handlePreview({
                                    id: value.ID || '',
                                    Workspace: value.Workspace,
                                    type: value.Type,
                                    route: value.Route,
                                    sites: value.Sites
                                })
                            }
                        >
                            <VisibilityIcon />
                        </IconButton>
                    </FormControl>
                </Card>
            </div>
        </>
    )
}

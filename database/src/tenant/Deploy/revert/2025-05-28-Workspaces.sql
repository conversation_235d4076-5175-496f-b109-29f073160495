-- Revert cm_tenant_db_DeploySchema:2025-05-20-Workspaces from pg

BEGIN;
    -- disable triggers
    ALTER TABLE content DISABLE TRIGGER ALL;

    -- Step 1: Delete all rows where workspace is not 'live'
    DELETE FROM public.content WHERE workspace != 'live';
    DELETE FROM public.content_history WHERE workspace != 'live';

    -- Step 2: Drop the composite primary key constraint
    ALTER TABLE public.content
        DROP CONSTRAINT pk_content;

    -- Step 3: Add back the original primary key constraint on just the id column
    ALTER TABLE public.content
        ADD CONSTRAINT pk_content PRIMARY KEY (id);

    -- Drop the workspaces table and related columns
    DROP TABLE IF EXISTS workspaces;

    ALTER TABLE content DROP COLUMN IF EXISTS workspace;
    ALTER TABLE content DROP COLUMN IF EXISTS effective_in;
    ALTER TABLE content_history DROP COLUMN IF EXISTS workspace;
    ALTER TABLE content_history DROP COLUMN IF EXISTS effective_in;

    ALTER TABLE navigation ADD CONSTRAINT fk_content_id
        FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE;

    ALTER TABLE legacy_url ADD CONSTRAINT fk_legacy_url_content_id
        FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE;

    ALTER TABLE media ADD CONSTRAINT fk_media_content_id
        FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE;


    -- enable triggers
    ALTER TABLE content ENABLE TRIGGER ALL;
COMMIT;

package admin2

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/auth/permissions"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/reservation"
	"contentmanager/pkgs/workspaces/handlers"
	shared2 "contentmanager/pkgs/workspaces/shared"
	"encoding/json"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"strings"
)

type ContentDTO struct {
	ID *uuid.UUID // required for new workspace version

	content.Base
	Workspace   string `binding:"required,min=3"`
	Type        commonModels.ContentType
	Title       string
	StructureID *uuid.UUID
	Data        json.RawMessage
	// Content specific fields (Page, News, Event)
	Structure  json.RawMessage
	Content    string
	Route      string
	Path       string
	PageLayout commonModels.PageType
	MediaID    *uuid.UUID
	Settings   json.RawMessage
	Meta       datatypes.JSONType[map[string]string]
	Tags       []uuid.UUID
	Structures []uuid.UUID

	//commonModels.ReservableParams
	//
	//SessionTimestamp     *time.Time
	//NextSessionTimestamp *time.Time
}

func (b ContentDTO) GetScopeEntity() string {
	switch b.Type {
	case commonModels.Template, commonModels.CSS, commonModels.JS:
		return "cm.resource." + string(b.Type)
	case commonModels.Page, commonModels.DistributedPage, commonModels.ExternalLinkContentType:
		return "cm.content.page"
	case commonModels.Fragment:
		return "cm.fragment"
	default:
		return "cm.content." + string(b.Type)
	}
}

func CreateContent(r *shared.AppContext, contentDTO ContentDTO) result.Result[uuid.UUID] {
	newID := uuid.NewV4()
	if contentDTO.ID != nil {
		// if it's a clone of "live" workspace, use the same ID
		newID = *contentDTO.ID
	}

	pathSuffix := strings.ReplaceAll(newID.String(), "-", "")
	if !strings.HasSuffix(contentDTO.Path, pathSuffix) {
		if len(contentDTO.Path) > 0 {
			contentDTO.Path += "." + pathSuffix
		} else {
			contentDTO.Path = pathSuffix
		}
	}

	c := content.Content{
		ID:        newID,
		Workspace: contentDTO.Workspace,
		// EffectiveIn will be assigned by the workspaces package
		Base:        contentDTO.Base,
		Active:      true,
		Type:        contentDTO.Type,
		Title:       contentDTO.Title,
		Content:     contentDTO.Content,
		Data:        contentDTO.Data,
		Structure:   contentDTO.Structure,
		Route:       contentDTO.Route,
		Path:        contentDTO.Path,
		PageLayout:  contentDTO.PageLayout,
		Approved:    true,
		MediaID:     contentDTO.MediaID,
		Settings:    contentDTO.Settings,
		StructureID: contentDTO.StructureID,
		Meta:        contentDTO.Meta,
		Structures:  contentDTO.Structures,
		Tags:        contentDTO.Tags,
	}
	c.Owner = r.Account().ID
	c.Publisher = r.Account().ID
	c.Created = r.AppTime().NowUTC()
	c.Updated = r.AppTime().NowUTC()

	if err := permissions.ValidateRoute(r.TenantDatabase(), c); err != nil {
		return result.Error(err, uuid.Nil)
	}
	if c.StructureID != nil {
		if err := ValidateData(r.TenantDatabase(), c, *c.StructureID); err != nil {
			return result.Error(err, uuid.Nil)
		}
	}

	if err := handlers.CreateWorkspacedEntity(r, &c); err != nil {
		return result.Error(err, uuid.Nil)
	}

	onContentChanged(r, c)

	return result.Success(c.ID)
}

func UpdateContent(r *shared.AppContext, key shared2.WorkspacedKey, contentDTO ContentDTO, params reservation.ReservableParams) result.EmptyResult {
	var c content.Content
	if err := r.TenantDatabase().Where("id = ? AND workspace = ?", key.ID, key.Workspace).First(&c).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if err := c.StartOrExtendSession(params, r.Account()); err != nil {
		return result.ErrorEmpty(err)
	}

	originalPath := c.Path
	newPath := contentDTO.Path

	c.Base = contentDTO.Base
	c.Type = contentDTO.Type
	c.Title = contentDTO.Title
	c.StructureID = contentDTO.StructureID
	c.Data = contentDTO.Data
	c.PublishAt = contentDTO.PublishAt
	c.ExpireAt = contentDTO.ExpireAt
	c.Tags = contentDTO.Tags

	if contentDTO.Type != commonModels.Fragment {
		c.Structure = contentDTO.Structure
		c.Content = contentDTO.Content
		c.Route = contentDTO.Route
		c.Path = contentDTO.Path
		c.PageLayout = contentDTO.PageLayout
		c.MediaID = contentDTO.MediaID
		c.Settings = contentDTO.Settings
		c.Meta = contentDTO.Meta
		c.Structures = contentDTO.Structures
	}

	if err := permissions.ValidateRoute(r.TenantDatabase(), c); err != nil {
		return result.ErrorEmpty(err)
	}

	if c.StructureID != nil {
		if err := ValidateData(r.TenantDatabase(), c, *c.StructureID); err != nil {
			return result.ErrorEmpty(err)
		}
	}

	c.Publisher = r.Account().ID
	c.Updated = r.AppTime().NowUTC()

	if txErr := r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		res := tx.Save(&c).Where("editing_session = ?", params.EditingSession)
		if res.Error != nil {
			return res.Error
		}
		if res.RowsAffected == 0 {
			return errors.New("error updating content: session out of sync")
		}

		// TODO: @Anatoly TBD
		if contentDTO.Type == commonModels.Template && originalPath != newPath && contentDTO.Workspace == "live" {
			if err := tx.
				Exec(" "+
					"UPDATE content "+
					"SET path = replace(path::text, ?::text, ?::text)::ltree "+
					"WHERE ? @> path ", originalPath, newPath, originalPath).
				Error; err != nil {
				return err
			}
		}

		return nil
	}); txErr != nil {
		return result.ErrorEmpty(txErr)
	}

	onContentChanged(r, c)

	return result.SuccessEmpty()
}

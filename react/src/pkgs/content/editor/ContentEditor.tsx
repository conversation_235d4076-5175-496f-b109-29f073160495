import { useContentQueries } from '../queries'
import { useParams } from 'react-router-dom'
import { useEffect, useRef, useState, useMemo } from 'react'
import { BaseTemplate, TemplateSelector } from './components/TemplateSelector'
import FormRenderer from '../../form-renderer/FormRenderer'
import { StructureSelector } from '../../structure/StructureSelector'
import { FormStructure } from '../../structure/types'
import { asSecured } from '../../auth/permissions/securityMapping'
import { Alert, Box, Button, Card, Checkbox, FormControl, FormControlLabel, Grid, Typography } from '@mui/material'
import TextField from '@mui/material/TextField'
import { RouteEditor } from './components/RouteEditor'
import { SeoOptions } from '@/common/components/SeoOptions'
import AppAccordion from '../../../common/components/AppAccordion'
import { GoToNavigation } from '../../navigation/GoToNavigation'
import { useAppContext, useCurrentSite } from '../../auth/atoms'
import { LegacyUrls } from './components/LegacyUrls'
import { DistributedPageEditor } from '../distributed-page/DistributedPageEditor'
import { EntityScopeEnum } from '../../auth/entityScope'
import { EventDateTimePickerV2 } from '@/common/editor/EventDateTimePickerV2'
import { MetaEditor } from './components/MetaEditor'
import { ContentEditorSaveBar } from './ContentEditorSaveBar'
import { useStructuredContentValidators } from './useStructuredContentValidators'
import { Content, ContentType, PublishPeriod } from '../types'
import { useDisable } from '@/common/useDisable'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useInputDisabler } from './imported-content/useInputDisabler'
import { EditableFieldsList } from './imported-content/EditableFieldsList'
import { BaseForm } from '../BaseForm'
import { AlertNotification } from './components/AlertNotification'
import { PinNews } from '@/pkgs/content/editor/components/PinNews'
import { RevisionHistoryList } from '@/pkgs/content/RevisionHistoryList'
import { bannerHeightAtom } from '@/app/AppTopBar/InstagramErrorsBanner'
import { appTopBarHeightAtom } from '@/app/AppTopBar/AppTopBar'
import { useAtom } from 'jotai'
import PageContainer from '@/common/components/PageContainer'
import useContentDiff from './diff/useContentDiff'
import { DistributedListsForContent } from '@/pkgs/ordered-lists/distributed-lists/DistributedListsForContent'
import { ListsForContent } from '@/pkgs/ordered-lists/distributed-lists/ListsForContent'
import useMinimizeSidebarIconButton from './useMinimizeSidebarIconButton'
import { useStructureByIdQuery } from '@/pkgs/structure/queries'
import { TagsSelector } from '@/pkgs/system/tags/TagsSelector'
import { SearchTools } from '@/pkgs/search/SearchTools'
import { ContentIndexingConfig } from '@/pkgs/search/types'
import { ContentEditorLoading } from './ContentEditorLoading'

export const defaultSEOAccordionId = 'defaultSEOAccordion'
export const defaultMetaAccordionId = 'defaultMetaAccordion'

function getDefaultAccordionState(expanded = true) {
    return {
        [defaultSEOAccordionId]: expanded,
        [defaultMetaAccordionId]: expanded === undefined ? false : expanded
    }
}

export const ContentEditor = () => {
    const appContext = useAppContext()
    const canUpdateDistributed = appContext.action(
        {
            Sites: null,
            DepartmentID: null,
            EntityScope: EntityScopeEnum.List
        },
        'update'
    )

    const [isEditMode, setIsEditMode] = useState<boolean>(false)

    const contentEditorRef = useRef<HTMLDivElement | null>(null)
    const { sidebarIsMinimized, MinimizeSidebarIconButton } = useMinimizeSidebarIconButton({
        anchorElement: contentEditorRef?.current
    })
    const [bannerHeight] = useAtom(bannerHeightAtom)
    const [appTopBarHeight] = useAtom(appTopBarHeightAtom)

    const [contentEditorIsLoading, setContentEditorIsLoading] = useState(false)

    useEffect(() => {
        if (contentEditorIsLoading) {
            setTimeout(() => {
                setContentEditorIsLoading(false)
            }, 1000)
        }
    }, [contentEditorIsLoading])

    const { id, workspace } = useParams()
    const currentSite = useCurrentSite()

    const formRendererRef = useRef<any>()
    const [accordionExpanded, setAccordionExpanded] = useState(getDefaultAccordionState(true))

    function setAllAccordions(expanded) {
        formRendererRef?.current?.setAllAccordions(expanded)
        setAccordionExpanded(getDefaultAccordionState(expanded))
    }

    const [state, setState] = useState<Content | undefined>(undefined)

    // stateStructure is used for initial loading when structure selector is not rendered (sidebarIsMinimized == true)
    const { data: stateStructure } = useStructureByIdQuery(state?.StructureID || '')

    const [structure, setStructure] = useState<FormStructure[] | undefined>(undefined)
    const [selectedTemplate, setSelectedTemplate] = useState<BaseTemplate | undefined>(undefined)
    const [distributedPageHasChanges, setDistributedPageHasChanges] = useState(false)

    const { validateAll, errors, setErrors } = useStructuredContentValidators(
        ['Sites', 'Route', 'Title', 'Path', 'StructureID', 'Settings', 'PublishAt', 'ExpireAt', 'Tags'],
        state,
        formRendererRef
    )

    const [notificationErrors, setNotificationErrors] = useState<Partial<Record<string, string>>>({})
    const validateNotification = () => {
        const validationErrors: Partial<Record<string, string>> = {}
        if (state?.Settings?.HasEmailNotification) {
            if (!state?.Settings?.EmailSubject) {
                validationErrors.EmailSubject = 'Email Subject is required'
            }
            if (!state?.Settings?.EmailBody) {
                validationErrors.EmailBody = 'Email Body is required'
            }
        }
        setNotificationErrors(validationErrors)
        return Object.keys(validationErrors).length === 0
    }

    const { fetcher, updater } = useContentQueries({ id, workspace })

    const { changed, renderContentDiffViewer, setContentDiffViewerIsOpen } = useContentDiff({
        serverValue: fetcher.data,
        value: state,
        hasExternalChanges: distributedPageHasChanges,
        title: 'Saved State',
        onRevert: (newValue) => {
            console.log('onRevert called with newValue:', newValue)
            console.log('newValue.Workspace:', newValue.Workspace)
            console.log('onRevert stack trace:', new Error().stack)
            setState({ ...newValue })
            setContentEditorIsLoading(true)
        }
    })

    const classifications = useMemo(
        () => (fetcher.data?.Type === 'page' ? ['page', 'template'] : [fetcher.data?.Type || 'page']),
        [fetcher.data]
    )

    useDisable()

    const hasPermission = useMemo(() => appContext.action(state, 'update'), [state])
    const hasDistributedPagePermission = useMemo(
        () =>
            appContext.action(
                { EntityScope: EntityScopeEnum.Page, Sites: [currentSite?.ID], DepartmentID: null },
                'update'
            ),
        [currentSite]
    )

    const { isInputDisabled, isImported, importInfo } = useInputDisabler({ content: state, hasPermission, isEditMode })

    useEffect(() => {
        if (updater.error) {
            const errorMessage = guessErrorMessage(updater.error)
            if (errorMessage.includes('Route')) {
                setErrors((prev) => ({ ...prev, Route: errorMessage }))
            }
            formRendererRef?.current?.processServerError(updater.error)
        } else {
            setErrors((prev) => ({ ...prev, Route: '' }))
        }
    }, [updater.error])

    useEffect(() => {
        if (!fetcher.data || fetcher.isLoading || fetcher.isRefetching) {
            console.log('Setting state to undefined due to loading/refetching')
            setState(undefined)
        } else {
            console.log('Setting state from fetcher.data:', fetcher.data)
            console.log('fetcher.data.Workspace:', fetcher.data.Workspace)
            setState(fetcher.data)
        }
    }, [fetcher.data])

    useEffect(() => {
        if (!state) return
        document.title = state.Title
    }, [state?.Title])

    useEffect(() => {
        if (!state) return

        console.log(`state.Workspace = '${state?.Workspace}'`)
        console.log('Full state object:', state)
        console.log('Stack trace:', new Error().stack)
    }, [state?.Workspace])

    if (contentEditorIsLoading) {
        return <ContentEditorLoading />
    }

    return (
        <Box>
            {fetcher.data && state && (
                <ContentEditorSaveBar
                    serverValue={fetcher.data}
                    value={state}
                    hasExternalChanges={distributedPageHasChanges}
                    onAction={(action, state) => {
                        console.log('onAction called with action:', action)
                        console.log('onAction called with state:', state)
                        console.log('onAction state.Workspace:', state.Workspace)

                        if (!validateAll() || !validateNotification()) {
                            return
                        }

                        if (action === 'save') {
                            console.log('About to call updater.mutate with state:', state)
                            console.log('updater.mutate state.Workspace:', state.Workspace)
                            hasPermission && updater.mutate(state)
                        }
                    }}
                    disabled={!hasPermission || !isEditMode}
                    onChange={(publishPeriod: PublishPeriod) => {
                        setState({ ...state, PublishAt: publishPeriod.PublishAt, ExpireAt: publishPeriod.ExpireAt })
                    }}
                    onChangeMode={(reservable, checked) => {
                        if (state) {
                            setState({ ...state, ...reservable })
                        }
                        setIsEditMode(checked)
                    }}
                    onRevert={(newValue) => {
                        console.log('ContentEditorSaveBar onRevert called with newValue:', newValue)
                        console.log('ContentEditorSaveBar newValue.Workspace:', newValue.Workspace)
                        console.log('ContentEditorSaveBar onRevert stack trace:', new Error().stack)
                        setState({ ...newValue })
                        setContentEditorIsLoading(true)
                    }}
                />
            )}

            <PageContainer>
                {fetcher.isLoading && <div>Loading...</div>}
                {fetcher.isError && <div>Error: {fetcher.error.message}</div>}

                {state && (
                    <Grid container spacing={2} marginTop={`${bannerHeight + appTopBarHeight}px`}>
                        {!sidebarIsMinimized && (
                            <Grid container item xs={4} data-testid='content-editor-left-column'>
                                <Grid item xs={12}>
                                    {isImported && (
                                        <EditableFieldsList hasPermission={hasPermission} importInfo={importInfo} />
                                    )}
                                    {changed ? (
                                        <Button
                                            sx={{ height: '36px', width: '100%', display: 'flex', marginBottom: 1 }}
                                            color={'warning'}
                                            variant={'contained'}
                                            onClick={() => {
                                                setContentDiffViewerIsOpen(true)
                                            }}
                                        >
                                            You have unsaved changes
                                        </Button>
                                    ) : null}
                                    {renderContentDiffViewer()}
                                    <Card sx={{ p: 2 }}>
                                        {!hasPermission && (
                                            <Alert severity={'info'} sx={{ marginBottom: 1 }}>
                                                {hasDistributedPagePermission && state?.Settings?.isDistrictPage
                                                    ? `You are using a Distributed Page. A portion of this page is locked. Please scroll down to add or edit your content.`
                                                    : 'You dont have access to this page - this is a read-only view.'}
                                            </Alert>
                                        )}

                                        <FormControl fullWidth sx={{ my: 1 }}>
                                            <TextField
                                                label='Title'
                                                required
                                                style={{ width: '100%' }}
                                                value={state.Title || ''}
                                                onChange={(v) => {
                                                    state && setState({ ...state, Title: v.target.value })
                                                }}
                                                disabled={isInputDisabled('title')}
                                                error={!!errors.Title}
                                            />
                                        </FormControl>
                                        {state.Type !== 'alert' && (
                                            <FormControl fullWidth sx={{ my: 1 }}>
                                                <RouteEditor
                                                    value={state.Route}
                                                    onChange={(v) => {
                                                        state && setState({ ...state, Route: v })
                                                    }}
                                                    contentType={state.Type as ContentType}
                                                    disabled={isInputDisabled('route')}
                                                    error={errors.Route}
                                                />
                                            </FormControl>
                                        )}

                                        {state.Type === 'news' && (
                                            <PinNews
                                                value={
                                                    Number.isFinite(state.Settings?.priority)
                                                        ? state.Settings.priority
                                                        : null
                                                }
                                                onChange={(v) =>
                                                    setState({
                                                        ...state,
                                                        Settings: { ...state.Settings, priority: v }
                                                    })
                                                }
                                                disabled={!hasPermission || !isEditMode}
                                            />
                                        )}
                                    </Card>
                                    <Card sx={{ my: 1, p: 2 }}>
                                        <FormControl fullWidth sx={{ my: 1 }}>
                                            <TemplateSelector
                                                ownerID={state.ID}
                                                path={state.Path}
                                                onChange={(path, tpl) => {
                                                    setState({ ...state, Path: path })
                                                    setSelectedTemplate(tpl)
                                                }}
                                                onLoaded={(tpl) => setSelectedTemplate(tpl)}
                                                templateType={'all'}
                                                classifications={classifications}
                                                error={errors.Path}
                                                disabled={isInputDisabled('path')}
                                            />
                                        </FormControl>
                                        <FormControl fullWidth sx={{ my: 1 }}>
                                            <StructureSelector
                                                required
                                                value={state.StructureID}
                                                onChange={(v, s) => {
                                                    setState({ ...state, StructureID: v || null })
                                                    setStructure(s?.FormStructure)
                                                }}
                                                selectedStructure={(s) => !structure && setStructure(s.FormStructure)}
                                                disabled={isInputDisabled('structure')}
                                                error={errors.StructureID}
                                                allowedStructures={selectedTemplate?.Structures || []}
                                            />
                                        </FormControl>
                                    </Card>

                                    {state.Type === 'event' && (
                                        <Card sx={{ my: 1, p: 2 }}>
                                            <EventDateTimePickerV2
                                                value={state.Settings}
                                                onChange={(v) => {
                                                    setState({
                                                        ...state,
                                                        Settings: {
                                                            ...(state?.Settings || {}),
                                                            startdate: v.startdate,
                                                            enddate: v.enddate,
                                                            isAllDay: v.isAllDay,
                                                            rrule: v.rrule,
                                                            exdate: v.exdate,
                                                            rdate: v.rdate
                                                        }
                                                    })
                                                }}
                                                disabled={Boolean(!hasPermission || isImported || !isEditMode)}
                                                error={errors.Settings} // TODO: add error message for event date
                                            />
                                            <div style={{ marginTop: '12px' }}>
                                                <TextField
                                                    label={'Location'}
                                                    placeholder={'Where will this event take place?'}
                                                    value={state.Settings?.location?.displayName || ''}
                                                    onChange={(v) => {
                                                        setState({
                                                            ...state,
                                                            Settings: {
                                                                ...(state?.Settings || {}),
                                                                location: {
                                                                    displayName: v?.target?.value || ''
                                                                }
                                                            }
                                                        })
                                                    }}
                                                    disabled={Boolean(!hasPermission || isImported || !isEditMode)}
                                                />
                                            </div>
                                        </Card>
                                    )}

                                    <Card sx={{ my: 1, p: 2 }}>
                                        <BaseForm
                                            value={{
                                                PublishAt: state.PublishAt,
                                                ExpireAt: state.ExpireAt,
                                                PrivacyLevel: state.PrivacyLevel,
                                                Sites: state.Sites,
                                                DepartmentID: state.DepartmentID
                                            }}
                                            onChange={(b) => {
                                                setState({
                                                    ...state,
                                                    PublishAt: b.PublishAt,
                                                    ExpireAt: b.ExpireAt,
                                                    PrivacyLevel: b.PrivacyLevel,
                                                    Sites: b.Sites,
                                                    DepartmentID: b.DepartmentID
                                                })
                                            }}
                                            contentType={asSecured(state).EntityScope}
                                            disabledFields={{
                                                Sites: isInputDisabled('site'),
                                                DepartmentID: isInputDisabled('departmentId'),
                                                PrivacyLevel: isInputDisabled('privacyLevel'),
                                                PublishAt: isInputDisabled('publishAt') || isInputDisabled('published'),
                                                ExpireAt: isInputDisabled('expireAt') || isInputDisabled('published')
                                            }}
                                            errors={errors}
                                        />
                                    </Card>

                                    <Card sx={{ my: 1, p: 2 }}>
                                        <FormControl fullWidth sx={{ my: 1 }}>
                                            <TagsSelector
                                                selected={state.Tags || []}
                                                disabled={isInputDisabled('tags')}
                                                tagTypes={[state.Type]}
                                                onChange={(tags) => {
                                                    tags && setState({ ...state, Tags: tags })
                                                }}
                                                hasError={!!errors.Tags}
                                            />
                                        </FormControl>
                                    </Card>

                                    {state.Type !== 'alert' &&
                                        (!!state.Settings?.isDistrictPage || state.Sites.length > 1) && (
                                            <Card sx={{ my: 1, p: 2 }}>
                                                <FormControlLabel
                                                    control={
                                                        <Checkbox
                                                            onChange={(e) => {
                                                                state &&
                                                                    setState({
                                                                        ...state,
                                                                        Settings: {
                                                                            ...(state.Settings || {}),
                                                                            isDistrictPage: e.target.checked
                                                                        }
                                                                    })
                                                            }}
                                                            checked={!!state.Settings?.isDistrictPage}
                                                            name='first'
                                                        />
                                                    }
                                                    label='Allow additional site specific content on shared sites'
                                                    disabled={Boolean(!hasPermission || isImported || !isEditMode)}
                                                />
                                            </Card>
                                        )}

                                    {state.Type === 'page' && (
                                        <Card sx={{ my: 1, p: 2 }}>
                                            <Alert severity={'info'} style={{ marginBottom: '0.5rem' }}>
                                                Navigation changes for {currentSite?.Name} can be done in the Navigation
                                                Editor
                                            </Alert>
                                            <GoToNavigation id={state.ID} active={state.Active} />
                                        </Card>
                                    )}

                                    {state.Type !== 'alert' && state && (
                                        <Card sx={{ my: 1, p: 2 }}>
                                            <LegacyUrls contentId={state.ID} hasPermission={hasPermission} />
                                        </Card>
                                    )}

                                    {state.Type === 'alert' && (
                                        <AlertNotification
                                            state={state}
                                            onChange={(e) => {
                                                setState({
                                                    ...state,
                                                    PrivacyLevel: e.target.checked ? 0 : state.PrivacyLevel,
                                                    Settings: {
                                                        ...(state.Settings || {}),
                                                        HasEmailNotification: e.target.checked
                                                    }
                                                })
                                            }}
                                            hasPermission={hasPermission}
                                            notificationErrors={notificationErrors}
                                            onChange1={(e) => {
                                                setState({
                                                    ...state,
                                                    Settings: {
                                                        ...(state.Settings || {}),
                                                        EmailSubject: e.target.value
                                                    }
                                                })
                                            }}
                                            onChange2={(e) => {
                                                setState({
                                                    ...state,
                                                    Settings: {
                                                        ...(state.Settings || {}),
                                                        EmailBody: e.target.value
                                                    }
                                                })
                                            }}
                                        />
                                    )}

                                    <AppAccordion
                                        unmountOnExit
                                        defaultExpanded={false}
                                        withoutPadding
                                        summary={'Lists'}
                                        details={
                                            <Box pt='12px'>
                                                {canUpdateDistributed && (
                                                    <>
                                                        <Typography
                                                            component={'p'}
                                                            sx={{ fontWeight: 'bold', marginLeft: '17px' }}
                                                        >
                                                            Distributed
                                                        </Typography>
                                                        <DistributedListsForContent
                                                            ContentID={state.ID}
                                                            ContentType={state.Type}
                                                        />
                                                    </>
                                                )}

                                                <Typography
                                                    component={'p'}
                                                    sx={{ fontWeight: 'bold', marginLeft: '17px' }}
                                                >
                                                    Individual
                                                </Typography>
                                                <ListsForContent ContentID={state.ID} ContentType={state.Type} />
                                            </Box>
                                        }
                                    />

                                    {(state.Type === 'page' || state.Type === 'news' || state.Type === 'event') && (
                                        <SearchTools
                                            contentID={state.ID}
                                            value={state.Settings?.ContentIndexingConfig}
                                            onChange={(config: ContentIndexingConfig) => {
                                                setState({
                                                    ...state,
                                                    Settings: {
                                                        ...state.Settings,
                                                        ContentIndexingConfig: config
                                                    }
                                                })
                                            }}
                                            isDistributed={!!state.Settings?.isDistrictPage}
                                        />
                                    )}

                                    <AppAccordion
                                        unmountOnExit
                                        defaultExpanded={false}
                                        withoutPadding
                                        summary={'Revision History'}
                                        details={
                                            <RevisionHistoryList
                                                content={state}
                                                onRevertHandler={(newValue) => {
                                                    console.log('RevisionHistoryList onRevertHandler called with newValue:', newValue)
                                                    console.log('RevisionHistoryList newValue.Workspace:', newValue.Workspace)
                                                    console.log('RevisionHistoryList onRevertHandler stack trace:', new Error().stack)
                                                    setState({ ...newValue })
                                                    setContentEditorIsLoading(true)
                                                }}
                                            />
                                        }
                                    />
                                </Grid>
                            </Grid>
                        )}

                        <Grid
                            container
                            item
                            xs={sidebarIsMinimized ? 0 : 8}
                            sx={{ mb: 30 }}
                            data-testid='content-editor-main'
                            mt={'-12px'}
                            ref={contentEditorRef}
                        >
                            <MinimizeSidebarIconButton />

                            <Grid item xs={12}>
                                {(structure || stateStructure?.FormStructure) && (
                                    <FormRenderer
                                        ref={formRendererRef}
                                        value={state.Data}
                                        onChange={(d) => {
                                            setState({ ...state, Data: d })
                                        }}
                                        formStructure={structure || stateStructure?.FormStructure}
                                        disabled={isInputDisabled('data')}
                                        accordionExpandedOnChange={(id, expanded) => {
                                            if (Array.isArray(id)) {
                                                setAccordionExpanded({
                                                    ...accordionExpanded,
                                                    ...(id.reduce(
                                                        (a, id, index) => ({
                                                            ...a,
                                                            [id]: expanded
                                                        }),
                                                        {}
                                                    ) || {})
                                                })
                                            } else {
                                                setAccordionExpanded({
                                                    ...accordionExpanded,
                                                    [id]: expanded
                                                })
                                            }
                                        }}
                                    />
                                )}

                                {currentSite?.ID && !!state.Settings?.isDistrictPage && (
                                    <DistributedPageEditor
                                        parentID={state.ID}
                                        siteID={currentSite?.ID}
                                        onChanges={(hasChanges) => {
                                            setDistributedPageHasChanges(hasChanges)
                                        }}
                                        label={`${state.Title} for ${currentSite?.Name}`}
                                        disabled={isImported}
                                    />
                                )}
                                <AppAccordion
                                    expanded={accordionExpanded[defaultSEOAccordionId]}
                                    onChangeHandler={(expanded) =>
                                        setAccordionExpanded({
                                            ...accordionExpanded,
                                            [defaultSEOAccordionId]: expanded
                                        })
                                    }
                                    allOnChangeHandler={(expanded) => {
                                        setAllAccordions(expanded)
                                    }}
                                    summary={
                                        <Typography component={'p'} variant='h5'>
                                            SEO
                                        </Typography>
                                    }
                                    details={
                                        <SeoOptions
                                            noWrapper
                                            value={{
                                                mediaId: state.MediaID,
                                                seoTitle: state.Settings?.seoTitle || '',
                                                seoDescription: state.Settings?.seoDescription || ''
                                            }}
                                            onChange={({ mediaId, seoTitle, seoDescription }) => {
                                                setState(
                                                    (prev) =>
                                                        prev && {
                                                            ...prev,
                                                            MediaID: mediaId,
                                                            Settings: {
                                                                ...prev?.Settings,
                                                                seoTitle,
                                                                seoDescription
                                                            }
                                                        }
                                                )
                                            }}
                                            disabled={!hasPermission || !isEditMode}
                                        />
                                    }
                                />
                                <AppAccordion
                                    expanded={accordionExpanded[defaultMetaAccordionId]}
                                    onChangeHandler={(expanded) =>
                                        setAccordionExpanded({
                                            ...accordionExpanded,
                                            [defaultMetaAccordionId]: expanded
                                        })
                                    }
                                    allOnChangeHandler={(expanded) => {
                                        setAllAccordions(expanded)
                                    }}
                                    summary={
                                        <Typography component={'div'} variant='h5'>
                                            Meta
                                        </Typography>
                                    }
                                    details={
                                        <MetaEditor
                                            value={state.Meta || {}}
                                            onChange={(v) => {
                                                setState({ ...state, Meta: v })
                                            }}
                                            disabled={!hasPermission || !isEditMode}
                                        />
                                    }
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                )}
            </PageContainer>
        </Box>
    )
}

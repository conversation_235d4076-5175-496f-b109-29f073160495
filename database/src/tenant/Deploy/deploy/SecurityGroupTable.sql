-- Deploy cm_tenant_db_DeploySchema:SecurityGroupTable to pg

BEGIN;

    CREATE TABLE security_group
    (
        id uuid NOT NULL DEFAULT uuid_generate_v4(),
        name character varying(256) NOT NULL,
        description TEXT,
        role_id uuid NOT NULL,
        site_id uuid,
        external_id_list text[] NOT NULL DEFAULT '{}',
        CONSTRAINT pk_security_group_id PRIMARY KEY(id),
        CONSTRAINT fk_security_group_role_id FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE
    )WITH (
        OIDS=FALSE
    );

    GRANT ALL ON TABLE security_group to contentmanager_application_user;

COMMIT;
package add_admin

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/content/admin2"
	"contentmanager/pkgs/content/resources/admin"
	"contentmanager/pkgs/reservation"
	shared2 "contentmanager/pkgs/workspaces/shared"
	"errors"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"os"
	"strings"
)

func AddContentV2(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v2/content", func(router httpService.Router) {

		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery admin2.SearchQuery
		}) {
			utils.WriteResultJSON(w, admin2.Search(r, params.FromQuery))
		})

		router.Post("/import/xlsx", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromMultipartForm admin2.ImportXLSXParams
		}) {
			if params.FromMultipartForm.File == nil {
				utils.WriteError(w, errors.New("File is required"))
				return
			}

			contentType := strings.ToLower(params.FromMultipartForm.File.Header.Get("Content-Type"))
			switch contentType {
			case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
				utils.WriteResultJSON(w, admin2.ImportXLSX(r, params.FromMultipartForm))
			case "text/calendar":
				utils.WriteResultJSON(w, admin2.ImportICS(r, params.FromMultipartForm))
			default:
				utils.WriteError(w, errors.New("Invalid file type. Expected XLSX or ICS"))
			}
		})

		router.Get("/export/xlsx", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery admin2.SearchQuery
		}) {
			result := admin2.Search(r, params.FromQuery)
			if result.IsError() {
				utils.WriteResponseJSON(w, nil, result.Unwrap())
				return
			}

			// Export to XLSX
			filePath, err := admin2.ExportToXLSX(result.Data.Rows)
			defer func(name string) {
				err := os.Remove(name)
				if err != nil {
					r.Logger().Error().Err(err).Msg("Failed to remove file")
				}
			}(filePath)

			if err != nil {
				r.Logger().Error().Err(err).Msg("Failed to export to XLSX")
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			if err := utils.WriteLocalFile(w, utils.WriteLocalFileParams{
				FilePath:    filePath,
				ContentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			}); err != nil {
				r.Logger().Error().Err(err).Msg("Failed to write local file")
				utils.WriteResponseJSON(w, nil, err)
			}
		})

		router.Get("/priority/max", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery admin2.SearchQuery
		}) {
			var maxPriority int

			err := r.TenantDatabase().Table("content").
				Select("COALESCE(MAX((settings->>'priority')::numeric), -1)").
				Where("active").
				Where("type='news'").
				Where("settings ? 'priority'").
				Where("(settings->>'priority')::numeric IS NOT NULL").
				Scan(&maxPriority).
				Error
			utils.WriteResponseJSON(w, maxPriority, err)
		})

		router.Post("/occurrences", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody admin2.OccurrencesParams
		}) {
			utils.WriteResultJSON(w, admin2.Occurrences(r, params.FromBody))
		})

		router.Get("/editors", func(w http.ResponseWriter, r *shared.AppContext) {
			utils.WriteResultJSON(w, admin2.GetContentEditors(r.TenantDatabase()))
		})

		router.Get("/preview/:id/:workspace", func(w http.ResponseWriter, r *shared.AppContext, p struct {
			bindauth.BindableParams
			FromPath struct {
				ID        uuid.UUID
				Workspace string
			}
		}) {
			var fromDB content.Content
			if err := r.TenantDatabase().Where("id = ?", p.FromPath.ID).Where(pgxx.Workspace(p.FromPath.Workspace, "")).First(&fromDB).Error; err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}

			url, err := admin2.GetContentPreviewURL(r, fromDB)
			if err != nil {
				http.Error(w, err.Error(), http.StatusBadRequest)
				return
			}
			http.Redirect(w, r.Request(), url, http.StatusFound)
		})

		router.Get("/:id/:workspace", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID        uuid.UUID `binding:"cm_uuid"`
				Workspace string
			}
		}) {
			utils.WriteResultJSON(w, admin2.GetByID(r, params.FromPath.ID, params.FromPath.Workspace))
		})

		/* CREATE */
		router.Post("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody admin2.ContentDTO
		}) {
			utils.WriteResultJSON(w, admin2.CreateContent(r, params.FromBody))
		})

		router.Post("/:id/:workspace/clone", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID        uuid.UUID `binding:"cm_uuid"`
				Workspace string
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.CloneContent(r, params.FromDB))
		})

		/* UPDATE */
		router.Put("/:id/:workspace", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath  shared2.WorkspacedKey
			FromQuery reservation.ReservableParams
			FromDB    content.Content
			FromBody  admin2.ContentDTO
		}) {
			utils.WriteResultJSON(w, admin2.UpdateContent(r, params.FromPath, params.FromBody, params.FromQuery))
		})

		/* PUBLISH */
		router.Post("/:id/:workspace", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID        uuid.UUID `binding:"cm_uuid"`
				Workspace string
			}
		}) {
			if params.FromPath.Workspace == "live" {
				http.Error(w, "Cannot publish live content. ", http.StatusBadRequest)
				return
			}

			utils.WriteResultJSON(w, admin2.PublishContent(r, params.FromPath.ID, params.FromPath.Workspace))
		})

		/* DELETE/RESTORE */
		router.Delete("/:id/:workspace", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID        uuid.UUID `binding:"cm_uuid"`
				Workspace string
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.Delete(r, params.FromDB))
		})

		router.Patch("/:id/:workspace/restore", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID        uuid.UUID `binding:"cm_uuid"`
				Workspace string    // can only be "live"
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.Restore(r, params.FromDB))
		})

		router.Patch("/:id/expire", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"cm_uuid"`
			}
			FromDB content.Content
		}) {
			utils.WriteResultJSON(w, admin2.Expire(r, params.FromPath.ID))
		})

		router.Get("/distributed-page/:parentID/:siteID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ParentID uuid.UUID `binding:"cm_uuid"`
				SiteID   uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, admin2.GetDistributedPage(r, params.FromPath.ParentID, params.FromPath.SiteID))
		})

		router.Put("/distributed-page/:parentID/:siteID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ParentID uuid.UUID `binding:"cm_uuid"`
				SiteID   uuid.UUID `binding:"cm_uuid"`
			}
			FromBody admin2.DistributedPageDTO
		}) {
			utils.WriteResultJSON(w, admin2.UpsertDistributedPage(r, params.FromPath.ParentID, params.FromPath.SiteID, params.FromBody))
		})

		router.Post("/resources", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody struct {
				Resources []string
			}
		}) {
			utils.WriteResultJSON(w, admin.GetEntitiesByResources(r.TenantDatabase(), params.FromBody.Resources))
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())
	return r
}

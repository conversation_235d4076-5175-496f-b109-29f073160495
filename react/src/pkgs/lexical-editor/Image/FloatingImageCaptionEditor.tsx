import React, { forwardRef, useEffect, useRef, useState } from 'react'
import { $positionFloatingMenu } from '../toolbar/toolbar.helpers'
import { _createPortal } from '../helpers/helpers'
import { Box, IconButton } from '@mui/material'
import { colours } from '../../../common/colours'
import { TextFieldDebounced } from '../../../common/components/TextFieldDebounced'
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined'
import EditOutlinedIcon from '@mui/icons-material/EditOutlined'

interface ImageCaptionTextFieldProps {
    imageRef: any
    value: string
    onChangeHandler: (value: string) => void
    imageCaptionTextFieldRef: React.MutableRefObject<HTMLDivElement | null>
}

export const FloatingImageCaptionEditor = forwardRef(
    ({ imageRef, value, onChangeHandler, imageCaptionTextFieldRef }: ImageCaptionTextFieldProps, ref) => {
        const [isEditMode, setisEditMode] = useState(false)
        const imageCaptionEditorRef = useRef(null)

        useEffect(() => {
            $positionFloatingMenu(imageCaptionEditorRef, imageRef, 'bottom')
        }, [imageRef])

        return _createPortal(
            <Box
                display='flex'
                ref={imageCaptionEditorRef}
                className='editorRef'
                sx={{
                    position: 'absolute',
                    zIndex: 1300,
                    top: '-10000px',
                    left: '-10000px',
                    marginTop: '-6px',
                    maxWidth: '280px',
                    width: '100%',
                    opacity: 0,
                    boxShadow: '0px 5px 10px rgba(0, 0, 0, 0.3)',
                    backgroundColor: colours.white,
                    borderRadius: '8px',
                    transition: 'opacity 0.5s',
                    alignItems: 'center'
                }}
            >
                <TextFieldDebounced
                    inputProps={{
                        style: {
                            color: isEditMode ? undefined : colours.disabled2
                        }
                    }}
                    onFocus={() => setisEditMode(true)}
                    placeholder={isEditMode ? 'Enter caption here' : 'Click here to edit'}
                    multiline
                    inputRef={imageCaptionTextFieldRef}
                    className='ImageNode__contentEditable'
                    value={value as any}
                    onChange={(ev) => onChangeHandler(ev.target.value)}
                    size='small'
                    onKeyDown={(event) => {
                        if (event.key === 'Enter' || event.key == 'Escape') {
                            event.preventDefault()
                            imageCaptionTextFieldRef?.current?.blur()
                            setisEditMode(false)
                        }
                    }}
                />
                <IconButton
                    tabIndex={0}
                    onMouseDown={(event) => event.preventDefault()}
                    onClick={() => {
                        if (isEditMode) {
                            imageCaptionTextFieldRef?.current?.blur()
                            setisEditMode(false)
                        } else {
                            setisEditMode(true)
                        }
                    }}
                    sx={{
                        marginLeft: '-2em',
                        borderRadius: '10px' // rounded-square,
                    }}
                    title={isEditMode ? 'Save' : 'Edit'}
                >
                    {isEditMode ? (
                        <CheckCircleOutlinedIcon sx={{ color: colours.base_blue }} />
                    ) : (
                        <EditOutlinedIcon sx={{ color: 'black' }} />
                    )}
                </IconButton>
            </Box>,
            document.body
        )
    }
)

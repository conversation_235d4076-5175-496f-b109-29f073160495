package reservation

import (
	"contentmanager/pkgs/auth/identity"
	"errors"
	uuid "github.com/satori/go.uuid"
	"time"
)

type (
	Reservable struct {
		CurrentEditor  *uuid.UUID
		EditingSession *time.Time
		ExtendedLock   *time.Time
	}
	ReservableEntity struct {
		Reservable
		ID        uuid.UUID
		Workspace string
	}
	ReservableParams struct {
		EditingSession     *time.Time
		NextEditingSession *time.Time
		ExtendedLock       *time.Time
	}
)

var (
	ErrEditingSessionConflict = errors.New("ERROR: a conflict was detected while attempting to reserve an editing session")
	ErrInvalidAccount         = errors.New("ERROR: cannot process reservation with an invalid account")
)

// IsEditingSessionForUserOrAvailable
// Should be kept in-sync with front-end function also named IsEditingSessionForUserOrAvailable
// r *shared.AppContext would be beneficial since we need AppTime and Account, but circular import breaks here.
func (res *Reservable) IsEditingSessionForUserOrAvailable(key *time.Time, accountID *uuid.UUID) bool {
	// If it's locked, only the CurrentEditor user can start a session or make changes.
	// Admin cannot override an editing session (EditingSession), only an extended lock.
	return !res.IsSessionLocked(key) && !res.isExtendedLocked(accountID)
}

func (res *Reservable) IsSessionLocked(key *time.Time) bool {
	if res.EditingSession == nil || !isFutureTimestamp(res.EditingSession) {
		return false
	}
	return !isMatchingKey(key, res.EditingSession)
}

func (res *Reservable) isExtendedLocked(accountID *uuid.UUID) bool {
	return isFutureTimestamp(res.ExtendedLock) && !isEqualComparable(res.CurrentEditor, accountID)
}

// StartOrExtendSession validates that the user can action on Reservable, and applies the modifications from Params.
func (res *Reservable) StartOrExtendSession(params ReservableParams, account *identity.Account) error {
	if account == nil {
		return ErrInvalidAccount
	}
	if !res.IsEditingSessionForUserOrAvailable(params.EditingSession, &account.ID) {
		return ErrEditingSessionConflict
	}
	res.apply(params, &account.ID)
	return nil
}

func (res *Reservable) apply(p ReservableParams, accountID *uuid.UUID) {
	if isFutureTimestamp(p.ExtendedLock) {
		res.ExtendedLock = p.ExtendedLock
	}
	if isFutureTimestamp(p.NextEditingSession) {
		utc := p.NextEditingSession.UTC()
		res.EditingSession = &utc
	} else {
		res.EditingSession = nil
	}
	if isFutureTimestamp(res.ExtendedLock) || isFutureTimestamp(res.EditingSession) {
		res.CurrentEditor = accountID
	} else {
		res.CurrentEditor = nil
	}
}

func isFutureTimestamp(t *time.Time) bool {
	if t == nil {
		return false
	}
	return time.Now().UTC().Before(t.UTC())
}

func isMatchingKey(t1 *time.Time, t2 *time.Time) bool {
	if t1 == t2 {
		return true
	}
	if t1 == nil || t2 == nil {
		return false
	}
	return t1.UTC().Equal(t2.UTC())
}

func isEqualComparable[T comparable](p1 *T, p2 *T) bool {
	// Same pointer or both nil
	if p1 == p2 {
		return true
	}
	// If they were both nil, the first if would return true
	if p1 == nil || p2 == nil {
		return false
	}
	// Compare the actual values
	return *p1 == *p2
}

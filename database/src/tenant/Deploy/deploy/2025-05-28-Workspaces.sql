-- Deploy cm_tenant_db_DeploySchema:2025-05-20-Workspaces to pg

BEGIN;
    -- disable triggers
    ALTER TABLE content DISABLE TRIGGER ALL;

    -- remove constrains
    DROP TABLE IF EXISTS content_tag;

    ALTER TABLE navigation DROP CONSTRAINT fk_content_id;
    ALTER TABLE legacy_url DROP CONSTRAINT fk_legacy_url_content_id;
    ALTER TABLE media DROP CONSTRAINT fk_media_content_id;

    -- create workspaces table
    CREATE TABLE IF NOT EXISTS workspaces
    (
        id varchar(50) NOT NULL PRIMARY KEY,
        description text,
        active boolean NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        created_by uuid NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_by uuid NOT NULL
    );
    GRANT ALL ON TABLE workspaces to contentmanager_application_user;

    -- seed the workspaces table
    INSERT INTO workspaces (id, description, active, created_at, created_by, updated_at, updated_by)
        VALUES ('live', 'Live content', true, now(), '45f06f48-a93c-414e-b9a0-7582e0abc085', now(), '45f06f48-a93c-414e-b9a0-7582e0abc085'),
               ('draft', 'Draft content', true, now(), '45f06f48-a93c-414e-b9a0-7582e0abc085', now(), '45f06f48-a93c-414e-b9a0-7582e0abc085');

    -- add workspace and effective_in columns to content
    ALTER TABLE content ADD COLUMN IF NOT EXISTS workspace varchar(50) not null default 'live';
    ALTER TABLE content ADD COLUMN IF NOT EXISTS effective_in varchar(50)[] not null default ARRAY['draft']::varchar[];
    -- add workspace and effective_in columns to content_history
    ALTER TABLE content_history ADD COLUMN IF NOT EXISTS workspace varchar(50) not null default 'live';
    ALTER TABLE content_history ADD COLUMN IF NOT EXISTS effective_in varchar(50)[] not null default ARRAY['draft']::varchar[];

    -- change the primary key constraint
    ALTER TABLE public.content
        DROP CONSTRAINT pk_content;
    ALTER TABLE public.content
        ADD CONSTRAINT pk_content PRIMARY KEY (id, workspace);

    -- add indexes
    CREATE INDEX idx_content_workspace ON content(workspace);
    CREATE INDEX idx_content_effective_in ON content USING GIN(effective_in);

    -- ??? Re-add the foreign key constraints
    -- ALTER TABLE navigation ADD CONSTRAINT fk_content_id
    --     FOREIGN KEY (content_id) REFERENCES content(id);
    --
    -- ALTER TABLE legacy_url ADD CONSTRAINT fk_legacy_url_content_id
    --     FOREIGN KEY (content_id) REFERENCES content(id);
    --
    -- ALTER TABLE media ADD CONSTRAINT fk_media_content_id
    --     FOREIGN KEY (content_id) REFERENCES content(id);

    -- enable triggers
    ALTER TABLE content ENABLE TRIGGER ALL;
COMMIT;

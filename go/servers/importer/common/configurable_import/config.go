package configurable_import

import (
	"encoding/json"
	"github.com/satori/go.uuid"
)

// ImportConfig is sourced from Settings.Data
// Settings Data for integrations that support configurable fields must include Id | News | Events
type (
	FromSettingsData struct {
		Id     string             `json:"id"`
		News   ContentFieldConfig `json:"news"`
		Events ContentFieldConfig `json:"events"`
	}
	ImportConfig struct {
		FromSettingsData
		SettingsID uuid.UUID
		Sites      []uuid.UUID
	}
)

func ValidateImportConfigJson(data json.RawMessage) (FromSettingsData, error) {
	var i FromSettingsData
	return i, json.Unmarshal(data, &i)
}
